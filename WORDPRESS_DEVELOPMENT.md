# WordPress Development with WP-CLI

This guide explains the best ways to work with WordPress instances in the WP-CLI development environment.

## 🎯 **Best Practices for WP-CLI Development**

Since the container doesn't include WordPress by default, you have several options for setting up WordPress instances to test WP-CLI commands:

### **Option 1: Quick Setup with <PERSON><PERSON> (Recommended)**

Use the provided `wp-dev-setup.sh` script for easy WordPress management:

```bash
# Enter the development container
./docker-dev.sh shell

# Set up WordPress in a subdirectory
./wp-dev-setup.sh setup

# Run WP-CLI commands
./wp-dev-setup.sh wp user list
./wp-dev-setup.sh wp plugin list
./wp-dev-setup.sh wp theme list

# Start development server
./wp-dev-setup.sh serve
```

### **Option 2: Manual Setup (For Custom Configurations)**

```bash
# Enter the development container
./docker-dev.sh shell

# Create a WordPress directory
mkdir -p wordpress-test && cd wordpress-test

# Download WordPress
../vendor/bin/wp core download

# Create wp-config.php
../vendor/bin/wp config create \
    --dbname=wp_cli_dev \
    --dbuser=wp_cli_dev \
    --dbpass=wp_cli_dev_password \
    --dbhost=mysql

# Install WordPress
../vendor/bin/wp core install \
    --url=localhost:8080/wordpress-test \
    --title="WP-CLI Dev Site" \
    --admin_user=admin \
    --admin_password=admin \
    --admin_email=<EMAIL>

# Test WP-CLI commands
../vendor/bin/wp user list
../vendor/bin/wp plugin list
```

### **Option 3: Multiple WordPress Instances**

You can create multiple WordPress instances for different testing scenarios:

```bash
# Set up different WordPress versions
./wp-dev-setup.sh setup --dir=wp-latest --title="Latest WordPress"
./wp-dev-setup.sh setup --dir=wp-test --title="Test Site" --db-name=wp_cli_test_1

# Work with specific instances
cd wp-latest && ../vendor/bin/wp --info
cd wp-test && ../vendor/bin/wp plugin install woocommerce
```

## 🚀 **Development Workflow**

### **1. Start the Environment**
```bash
./docker-dev.sh up
```

### **2. Set Up WordPress**
```bash
./docker-dev.sh shell
./wp-dev-setup.sh setup
```

### **3. Start Development Server**
```bash
./wp-dev-setup.sh serve
```

### **4. Access Your Sites**
- **WordPress Site**: http://localhost:8080/wordpress-test
- **WordPress Admin**: http://localhost:8080/wordpress-test/wp-admin
- **phpMyAdmin**: http://localhost:8081
- **Credentials**: admin/admin

### **5. Develop and Test WP-CLI Commands**
```bash
# Test existing commands
./wp-dev-setup.sh wp user list
./wp-dev-setup.sh wp post list
./wp-dev-setup.sh wp plugin list

# Install and test plugins
./wp-dev-setup.sh wp plugin install woocommerce --activate
./wp-dev-setup.sh wp plugin list

# Test database operations
./wp-dev-setup.sh wp db query "SELECT * FROM wp_users"
./wp-dev-setup.sh wp search-replace "old-domain.com" "new-domain.com"
```

## 🛠 **Available Helper Commands**

The `wp-dev-setup.sh` script provides these commands:

- `setup` - Set up a new WordPress installation
- `clean` - Remove WordPress installation  
- `reset` - Clean and setup fresh installation
- `serve` - Start PHP development server
- `wp` - Run WP-CLI commands in the WordPress directory
- `shell` - Enter the development container

### **Examples:**
```bash
# Quick setup
./wp-dev-setup.sh setup

# Custom setup
./wp-dev-setup.sh setup --dir=my-site --title="My Test Site"

# Run WP-CLI commands
./wp-dev-setup.sh wp user <NAME_EMAIL>
./wp-dev-setup.sh wp post create --post_title="Test Post"

# Clean up
./wp-dev-setup.sh clean
```

## 📁 **Directory Structure**

```
wp-cli-dev/
├── wordpress-test/          # Default WordPress installation
│   ├── wp-config.php
│   ├── wp-content/
│   └── ...
├── vendor/                  # Composer dependencies
├── wp-cli/                  # WP-CLI source code
├── wp-dev-setup.sh         # Helper script
└── docker-dev.sh           # Docker management script
```

## 🔧 **Database Configuration**

The environment includes multiple databases for testing:

- `wp_cli_dev` - Main development database
- `wp_cli_test_1`, `wp_cli_test_2`, `wp_cli_test_3` - Additional test databases
- `wordpress_test` - WordPress container database

### **Database Access:**
```bash
# From container
mariadb -h mysql -u wp_cli_dev -p
# Password: wp_cli_dev_password

# Via phpMyAdmin
# URL: http://localhost:8081
# Username: root
# Password: root_password
```

## 🧪 **Testing WP-CLI Development**

### **Run Tests:**
```bash
# Enter container
./docker-dev.sh shell

# Run all tests
composer test

# Run specific test suites
composer phpunit
composer behat
composer lint
composer phpcs
```

### **Test Your Changes:**
```bash
# Make changes to WP-CLI code in wp-cli/ directory
# Test with your WordPress instance
./wp-dev-setup.sh wp your-new-command

# Run automated tests
vendor/bin/phpunit
vendor/bin/behat
```

## 💡 **Tips for Effective Development**

1. **Use Multiple WordPress Instances**: Create different setups for different testing scenarios
2. **Leverage the Helper Script**: Use `wp-dev-setup.sh` for quick operations
3. **Keep the Server Running**: Use `./wp-dev-setup.sh serve` in one terminal, develop in another
4. **Use Database Snapshots**: Export/import databases for consistent testing
5. **Test with Different WordPress Versions**: Download specific versions for compatibility testing

## 🔄 **Common Workflows**

### **Plugin Development Testing:**
```bash
./wp-dev-setup.sh wp plugin install your-plugin --activate
./wp-dev-setup.sh wp plugin list
./wp-dev-setup.sh wp your-plugin-command
```

### **Theme Development Testing:**
```bash
./wp-dev-setup.sh wp theme install twentytwentyfour --activate
./wp-dev-setup.sh wp theme list
```

### **Database Testing:**
```bash
./wp-dev-setup.sh wp db export backup.sql
./wp-dev-setup.sh wp db import test-data.sql
./wp-dev-setup.sh wp search-replace "old.com" "new.com"
```

This setup provides a complete, flexible environment for WP-CLI development with easy WordPress instance management!
