<?xml version="1.0"?>
<ruleset name="WP-CLI-Dev">
	<description>Custom ruleset for the WP-CLI development environment</description>

	<!--
	#############################################################################
	COMMAND LINE ARGUMENTS
	For help understanding this file: https://github.com/squizlabs/PHP_CodeSniffer/wiki/Annotated-ruleset.xml
	For help using PHPCS: https://github.com/squizlabs/PHP_CodeSniffer/wiki/Usage
	#############################################################################
	-->

	<!-- What to scan. -->
	<file>./.maintenance/</file>

	<!-- Show progress. -->
	<arg value="p"/>

	<!-- Strip the filepaths down to the relevant bit. -->
	<arg name="basepath" value="./"/>

	<!-- Check up to 8 files simultaneously. -->
	<arg name="parallel" value="8"/>

	<!--
	#############################################################################
	USE THE WP_CLI_CS RULESET
	#############################################################################
	-->

	<rule ref="WP_CLI_CS"/>

	<!--
	#############################################################################
	PROJECT SPECIFIC CONFIGURATION FOR SNIFFS
	#############################################################################
	-->

	<!-- For help understanding the `testVersion` configuration setting:
		 https://github.com/PHPCompatibility/PHPCompatibility#sniffing-your-code-for-compatibility-with-specific-php-versions -->
	<config name="testVersion" value="7.2-"/>

	<!--
	#############################################################################
	SELECTIVE EXCLUSIONS
	#############################################################################
	-->
	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<exclude-pattern>*/.maintenance/*</exclude-pattern>
	</rule>
	<rule ref="WordPress.WP.GlobalVariablesOverride.Prohibited">
		<exclude-pattern>*/.maintenance/*</exclude-pattern>
	</rule>

</ruleset>
