#!/bin/bash

# WP-CLI Development Environment Setup Script
# This script helps you quickly set up WordPress instances for testing WP-CLI commands

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Default values
WP_DIR="wordpress-test"
DB_NAME="wp_cli_dev"
DB_USER="wp_cli_dev"
DB_PASS="wp_cli_dev_password"
DB_HOST="mysql"
SITE_URL="localhost:8080"
ADMIN_USER="admin"
ADMIN_PASS="admin"
ADMIN_EMAIL="<EMAIL>"
SITE_TITLE="WP-CLI Dev Site"

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  setup     - Set up a new WordPress installation"
    echo "  clean     - Remove WordPress installation"
    echo "  reset     - Clean and setup fresh installation"
    echo "  serve     - Start PHP development server"
    echo "  wp        - Run WP-CLI commands in the WordPress directory"
    echo "  shell     - Enter the development container"
    echo ""
    echo "Options:"
    echo "  --dir=DIR         WordPress directory (default: wordpress-test)"
    echo "  --db-name=NAME    Database name (default: wp_cli_dev)"
    echo "  --url=URL         Site URL (default: localhost:8080)"
    echo "  --title=TITLE     Site title (default: 'WP-CLI Dev Site')"
    echo "  --admin-user=USER Admin username (default: admin)"
    echo "  --admin-pass=PASS Admin password (default: admin)"
    echo "  --admin-email=EMAIL Admin email (default: <EMAIL>)"
    echo ""
    echo "Examples:"
    echo "  $0 setup                           # Set up WordPress with defaults"
    echo "  $0 setup --dir=my-wp --title='My Site'"
    echo "  $0 wp user list                    # Run WP-CLI command"
    echo "  $0 wp plugin install woocommerce  # Install a plugin"
    echo "  $0 serve                           # Start development server"
}

# Parse command line arguments
COMMAND=""
while [[ $# -gt 0 ]]; do
    case $1 in
        setup|clean|reset|serve|wp|shell)
            COMMAND="$1"
            shift
            ;;
        --dir=*)
            WP_DIR="${1#*=}"
            shift
            ;;
        --db-name=*)
            DB_NAME="${1#*=}"
            shift
            ;;
        --url=*)
            SITE_URL="${1#*=}"
            shift
            ;;
        --title=*)
            SITE_TITLE="${1#*=}"
            shift
            ;;
        --admin-user=*)
            ADMIN_USER="${1#*=}"
            shift
            ;;
        --admin-pass=*)
            ADMIN_PASS="${1#*=}"
            shift
            ;;
        --admin-email=*)
            ADMIN_EMAIL="${1#*=}"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            if [[ "$COMMAND" == "wp" ]]; then
                # Pass remaining arguments to WP-CLI
                break
            else
                print_error "Unknown option: $1"
                show_usage
                exit 1
            fi
            ;;
    esac
done

# Function to check if we're in the container
check_container() {
    if [[ ! -f "/.dockerenv" ]]; then
        print_error "This script should be run inside the Docker container."
        print_status "Use: ./docker-dev.sh shell"
        exit 1
    fi
}

# Function to setup WordPress
setup_wordpress() {
    print_header "Setting up WordPress in $WP_DIR"

    # Create directory if it doesn't exist
    if [[ ! -d "$WP_DIR" ]]; then
        print_status "Creating directory: $WP_DIR"
        mkdir -p "$WP_DIR"
    fi

    cd "$WP_DIR"

    # Download WordPress if not already present
    if [[ ! -f "wp-config.php" && ! -f "wp-load.php" ]]; then
        print_status "Downloading WordPress..."
        ../vendor/bin/wp core download
    else
        print_warning "WordPress files already exist, skipping download"
    fi

    # Create wp-config.php if it doesn't exist
    if [[ ! -f "wp-config.php" ]]; then
        print_status "Creating wp-config.php..."
        ../vendor/bin/wp config create \
            --dbname="$DB_NAME" \
            --dbuser="$DB_USER" \
            --dbpass="$DB_PASS" \
            --dbhost="$DB_HOST"
    else
        print_warning "wp-config.php already exists, skipping creation"
    fi

    # Install WordPress if not already installed
    if ! ../vendor/bin/wp core is-installed 2>/dev/null; then
        print_status "Installing WordPress..."
        ../vendor/bin/wp core install \
            --url="$SITE_URL" \
            --title="$SITE_TITLE" \
            --admin_user="$ADMIN_USER" \
            --admin_password="$ADMIN_PASS" \
            --admin_email="$ADMIN_EMAIL"

        print_status "WordPress installed successfully!"
        print_status "Admin URL: http://$SITE_URL/wp-admin"
        print_status "Username: $ADMIN_USER"
        print_status "Password: $ADMIN_PASS"
    else
        print_warning "WordPress is already installed"
    fi

    cd ..
}

# Function to clean WordPress installation
clean_wordpress() {
    print_header "Cleaning WordPress installation in $WP_DIR"

    if [[ -d "$WP_DIR" ]]; then
        print_status "Removing directory: $WP_DIR"
        rm -rf "$WP_DIR"
        print_status "WordPress installation cleaned"
    else
        print_warning "Directory $WP_DIR does not exist"
    fi
}

# Function to reset WordPress installation
reset_wordpress() {
    print_header "Resetting WordPress installation"
    clean_wordpress
    setup_wordpress
}

# Function to start development server
start_server() {
    print_header "Starting PHP Development Server"
    print_status "Server will be available at: http://localhost:8080"
    print_status "WordPress site: http://localhost:8080"
    print_status "Press Ctrl+C to stop the server"
    echo ""

    # Create a simple router script for serving WordPress from subdirectory
    cat > router.php << 'EOF'
<?php
// Simple router for serving WordPress from subdirectory
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];

// Remove query string
$path = parse_url($request_uri, PHP_URL_PATH);

// If requesting root, redirect to WordPress directory
if ($path === '/' || $path === '') {
    header('Location: /wordpress-test/');
    exit;
}

// If requesting WordPress directory, serve from there
if (strpos($path, '/wordpress-test/') === 0) {
    $wp_path = substr($path, strlen('/wordpress-test'));
    if ($wp_path === '' || $wp_path === '/') {
        $wp_path = '/index.php';
    }

    $file_path = __DIR__ . '/wordpress-test' . $wp_path;

    if (is_file($file_path)) {
        // For PHP files, include them
        if (pathinfo($file_path, PATHINFO_EXTENSION) === 'php') {
            chdir(__DIR__ . '/wordpress-test');
            include $file_path;
            return true;
        } else {
            // For other files, serve them directly
            return false;
        }
    }
}

// For other requests, serve normally
return false;
EOF

    php -S 0.0.0.0:80 router.php
}

# Function to run WP-CLI commands
run_wp_cli() {
    check_container

    if [[ ! -d "$WP_DIR" ]]; then
        print_error "WordPress directory $WP_DIR does not exist"
        print_status "Run: $0 setup"
        exit 1
    fi

    cd "$WP_DIR"
    ../vendor/bin/wp "$@"
    cd ..
}

# Function to enter shell
enter_shell() {
    if [[ -f "/.dockerenv" ]]; then
        print_status "Already in container. Starting new shell..."
        bash
    else
        print_status "Entering Docker container..."
        exec ./docker-dev.sh shell
    fi
}

# Main execution
case "$COMMAND" in
    setup)
        check_container
        setup_wordpress
        ;;
    clean)
        check_container
        clean_wordpress
        ;;
    reset)
        check_container
        reset_wordpress
        ;;
    serve)
        check_container
        start_server
        ;;
    wp)
        run_wp_cli "$@"
        ;;
    shell)
        enter_shell
        ;;
    "")
        print_error "No command specified"
        show_usage
        exit 1
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
