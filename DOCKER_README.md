# WP-CLI Development Environment with Docker

This Docker setup provides a complete development environment for WP-CLI with PHP, MySQL, and phpMyAdmin. WordPress instances are created on-demand using the provided helper scripts.

## Services

- **PHP Container**: PHP 8.1 with all necessary extensions for WP-CLI development
- **MySQL**: MySQL 8.0 database server with multiple test databases
- **phpMyAdmin**: Web-based MySQL administration tool

## Quick Start

1. **Build and start the containers (with smart dependency management):**
   ```bash
   ./docker-dev.sh start
   ```

   Or manually:
   ```bash
   docker-compose up -d --build
   ```

2. **Access the PHP development container:**
   ```bash
   ./docker-dev.sh shell
   ```

   Or manually:
   ```bash
   docker-compose exec php bash
   ```

3. **Dependencies are automatically managed:**
   - Dependencies are only installed when needed (missing vendor/, outdated composer.lock, etc.)
   - Use `./docker-dev.sh install` to force reinstall dependencies
   - Composer cache and vendor directories are persisted in Docker volumes for faster rebuilds

## Access Points

- **phpMyAdmin**: <http://localhost:8080>
  - Username: `root`
  - Password: `root_password`

- **WordPress Development Sites**: Created on-demand using `wp-dev-setup.sh`
  - Default: <http://localhost:8080/wordpress-test> (when server is running)

- **PHP Built-in Server**: <http://localhost:8080> (started with `wp-dev-setup.sh serve`)

## Database Configuration

- **Host**: `mysql` (from within containers) or `localhost:3306` (from host)
- **Root Password**: `root_password`
- **Development Database**: `wp_cli_dev`
- **Development User**: `wp_cli_dev`
- **Development Password**: `wp_cli_dev_password`

### Available Test Databases

- `wp_cli_dev` - Main development database
- `wordpress_test` - WordPress test instance database
- `wp_cli_test_1`, `wp_cli_test_2`, `wp_cli_test_3` - Additional test databases

## Development Workflow

### Working with WP-CLI

1. **Set up WordPress for testing:**

   ```bash
   # Enter the container
   ./docker-dev.sh shell

   # Set up WordPress using the helper script
   ./wp-dev-setup.sh setup
   ```

2. **Run WP-CLI commands:**

   ```bash
   # Using the helper script (recommended)
   ./wp-dev-setup.sh wp user list
   ./wp-dev-setup.sh wp plugin list

   # Or directly in the WordPress directory
   cd wordpress-test && ../vendor/bin/wp user list
   ```

3. **Start development server:**

   ```bash
   ./wp-dev-setup.sh serve
   # Access at http://localhost:8080/wordpress-test
   ```

4. **Run tests:**

   ```bash
   # Run all tests
   composer test

   # Run specific test suites
   composer phpunit
   composer behat
   composer lint
   composer phpcs
   ```

### Database Operations

1. **Connect to MySQL from container:**
   ```bash
   docker-compose exec mysql mysql -u root -p
   # Password: root_password
   ```

2. **Import/Export databases:**
   ```bash
   # Export
   docker-compose exec mysql mysqldump -u root -p wp_cli_dev > backup.sql
   
   # Import
   docker-compose exec -T mysql mysql -u root -p wp_cli_dev < backup.sql
   ```

### File Editing

Your local files are mounted into the container at `/var/www/html`, so you can edit files on your host machine and changes will be reflected immediately in the container.

## Dependency Caching & Optimization

This setup includes several optimizations to avoid downloading dependencies every time:

### Smart Dependency Management
- Dependencies are only installed when actually needed:
  - `vendor/` directory is missing or empty
  - `composer.lock` is missing
  - `composer.json` is newer than `composer.lock`
  - Manual force install with `./docker-dev.sh install`

### Docker Volume Caching
- **Composer Cache**: `/root/.composer` is cached in a Docker volume
- **Vendor Directory**: `vendor/` is cached in a Docker volume
- **Node Modules**: `node_modules/` is cached in a Docker volume (if needed)

### Multi-stage Dockerfile
- Dependencies are installed in separate layers for better caching
- Only changed files trigger rebuilds of specific layers

### Usage Tips
```bash
# Start with smart dependency checking (recommended)
./docker-dev.sh start

# Force install dependencies (when needed)
./docker-dev.sh install

# Check if dependencies need updating
./docker-dev.sh status
```

## Useful Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f php
docker-compose logs -f mysql

# Rebuild containers
docker-compose up -d --build

# Clean up everything
docker-compose down -v --remove-orphans
docker system prune -a
```

## Troubleshooting

### Permission Issues
If you encounter permission issues, you can fix them by running:
```bash
docker-compose exec php chown -R www:www /var/www/html
```

### MySQL Connection Issues
If you can't connect to MySQL, ensure the container is running:
```bash
docker-compose ps
docker-compose logs mysql
```

### Composer Issues
If Composer dependencies fail to install:
```bash
docker-compose exec php composer clear-cache
docker-compose exec php composer install --no-cache
```

## Environment Variables

You can customize the environment by creating a `.env` file:

```env
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_DATABASE=your_database
MYSQL_USER=your_user
MYSQL_PASSWORD=your_password
PHP_VERSION=8.1
```
