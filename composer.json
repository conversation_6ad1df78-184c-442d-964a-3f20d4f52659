{"name": "wp-cli/wp-cli-dev", "description": "Sets up a WP-CLI development environment that allows for easy development across all packages.", "keywords": ["cli", "wordpress"], "homepage": "https://wp-cli.org", "license": "MIT", "repositories": [{"type": "path", "url": "php-cli-tools"}, {"type": "path", "url": "wp-cli"}, {"type": "path", "url": "wp-cli.github.com"}, {"type": "path", "url": "builds"}, {"type": "path", "url": "google-sitemap-generator-cli"}, {"type": "path", "url": "wp-super-cache-cli"}, {"type": "path", "url": "server-command"}, {"type": "path", "url": "restful"}, {"type": "path", "url": "scaffold-package-command"}, {"type": "path", "url": "doctor-command"}, {"type": "path", "url": "dist-archive-command"}, {"type": "path", "url": "profile-command"}, {"type": "path", "url": "admin-command"}, {"type": "path", "url": "handbook"}, {"type": "path", "url": "media-command"}, {"type": "path", "url": "find-command"}, {"type": "path", "url": "cron-command"}, {"type": "path", "url": "import-command"}, {"type": "path", "url": "db-command"}, {"type": "path", "url": "config-command"}, {"type": "path", "url": "spyc"}, {"type": "path", "url": "export-command"}, {"type": "path", "url": "package-command"}, {"type": "path", "url": "search-replace-command"}, {"type": "path", "url": "shell-command"}, {"type": "path", "url": "eval-command"}, {"type": "path", "url": "scaffold-command"}, {"type": "path", "url": "entity-command"}, {"type": "path", "url": "extension-command"}, {"type": "path", "url": "language-command"}, {"type": "path", "url": "super-admin-command"}, {"type": "path", "url": "rewrite-command"}, {"type": "path", "url": "cache-command"}, {"type": "path", "url": "checksum-command"}, {"type": "path", "url": "widget-command"}, {"type": "path", "url": "role-command"}, {"type": "path", "url": "core-command"}, {"type": "path", "url": "automated-tests"}, {"type": "path", "url": "rpm-build"}, {"type": "path", "url": "embed-command"}, {"type": "path", "url": "wp-config-transformer"}, {"type": "path", "url": "deb-build"}, {"type": "path", "url": "i18n-command"}, {"type": "path", "url": "wp-cli-tests"}, {"type": "path", "url": "wp-cli-bundle"}, {"type": "path", "url": "maintenance-mode-command"}], "require": {"php": ">=7.2.24", "ext-json": "*", "ext-dom": "*", "wp-cli/admin-command": "dev-main", "wp-cli/automated-tests": "dev-main", "wp-cli/cache-command": "dev-main", "wp-cli/checksum-command": "dev-main", "wp-cli/config-command": "dev-main", "wp-cli/core-command": "dev-main", "wp-cli/cron-command": "dev-main", "wp-cli/db-command": "dev-main", "wp-cli/dist-archive-command": "dev-main", "wp-cli/doctor-command": "dev-main", "wp-cli/embed-command": "dev-main", "wp-cli/entity-command": "dev-main", "wp-cli/eval-command": "dev-main", "wp-cli/export-command": "dev-main", "wp-cli/extension-command": "dev-main", "wp-cli/find-command": "dev-main", "wp-cli/google-sitemap-generator-cli": "dev-main", "wp-cli/i18n-command": "dev-main", "wp-cli/import-command": "dev-main", "wp-cli/language-command": "dev-main", "wp-cli/maintenance-mode-command": "dev-main", "wp-cli/media-command": "dev-main", "wp-cli/mustangostang-spyc": "dev-master as 0.6.x-dev", "wp-cli/package-command": "dev-main", "wp-cli/php-cli-tools": "dev-master as 0.12.x-dev", "wp-cli/profile-command": "dev-main", "wp-cli/restful": "dev-main", "wp-cli/rewrite-command": "dev-main", "wp-cli/role-command": "dev-main", "wp-cli/scaffold-command": "dev-main", "wp-cli/scaffold-package-command": "dev-main", "wp-cli/search-replace-command": "dev-main", "wp-cli/server-command": "dev-main", "wp-cli/shell-command": "dev-main", "wp-cli/super-admin-command": "dev-main", "wp-cli/widget-command": "dev-main", "wp-cli/wp-cli": "dev-main", "wp-cli/wp-cli-bundle": "dev-main", "wp-cli/wp-cli-tests": "dev-main", "wp-cli/wp-config-transformer": "dev-main as 1.2.x-dev", "wp-cli/wp-super-cache-cli": "dev-main", "johnpbloch/wordpress-core-installer": "^1.0 || ^2.0", "johnpbloch/wordpress-core": "dev-master"}, "require-dev": {"roave/security-advisories": "dev-latest"}, "suggest": {"psy/psysh": "Enhanced `wp shell` functionality"}, "config": {"process-timeout": 7200, "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "johnpbloch/wordpress-core-installer": true, "phpstan/extension-installer": true}}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "commands": ["maintenance", "maintenance contrib-list", "maintenance milestones-after", "maintenance milestones-since", "maintenance release-date", "maintenance release-notes", "maintenance replace-label"], "readme": {"sections": ["Installation", "Development", "Using", "Contributing", "Support"], "package_description": {"post": ".readme-partials/DESCRIPTION.md"}, "development": {"body": ".readme-partials/DEVELOPMENT.md"}, "installation": {"body": ".readme-partials/INSTALLATION.md"}, "show_powered_by": false}, "wordpress-install-dir": "wordpress-core"}, "autoload-dev": {"psr-4": {"WP_CLI\\Maintenance\\": ".maintenance/src/"}, "files": [".maintenance/bootstrap.php"]}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"pre-install-cmd": "php .maintenance/clone-all-repositories.php", "pre-update-cmd": "php .maintenance/clone-all-repositories.php", "post-install-cmd": ["php .maintenance/symlink-vendor-folders.php", "php .maintenance/phpstorm.exclude-recursive-folders.php"], "post-update-cmd": ["php .maintenance/symlink-vendor-folders.php", "php .maintenance/phpstorm.exclude-recursive-folders.php"], "behat": "run-behat-tests", "behat-rerun": "rerun-behat-tests", "lint": "run-linter-tests", "phpcs": "run-phpcs-tests", "phpunit": "run-php-unit-tests", "prepare-tests": "install-package-tests", "test": ["@lint", "@phpcs", "@phpunit", "@behat"]}, "support": {"issues": "https://github.com/wp-cli/wp-cli-bundle/issues", "source": "https://github.com/wp-cli/wp-cli-bundle", "docs": "https://make.wordpress.org/cli/handbook/"}}