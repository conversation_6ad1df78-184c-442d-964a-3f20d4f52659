
services:
  # PHP Development Environment
  php:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: wp-cli-dev-php
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
      - composer_cache:/root/.composer
      - vendor_cache:/var/www/html/vendor
      - node_modules_cache:/var/www/html/node_modules
    working_dir: /var/www/html
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_DATABASE=wp_cli_dev
      - MYSQL_USER=wp_cli_dev
      - MYSQL_PASSWORD=wp_cli_dev_password
      - MYSQL_ROOT_PASSWORD=root_password
      - WP_CLI_CACHE_DIR=/tmp/wp-cli-cache
      - COMPOSER_CACHE_DIR=/root/.composer
    depends_on:
      - mysql
    networks:
      - wp-cli-network
    ports:
      - "8080:80"
    stdin_open: true
    tty: true

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: wp-cli-dev-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: wp_cli_dev
      MYSQL_USER: wp_cli_dev
      MYSQL_PASSWORD: wp_cli_dev_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - wp-cli-network
    command: --default-authentication-plugin=mysql_native_password

  # phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: wp-cli-dev-phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - wp-cli-network



volumes:
  mysql_data:
  composer_cache:
  vendor_cache:
  node_modules_cache:

networks:
  wp-cli-network:
    driver: bridge
