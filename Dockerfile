# Base stage with common dependencies
FROM php:8.4.7-cli AS base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    wget \
    nano \
    mariadb-client \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions required for WP-CLI and WordPress
RUN docker-php-ext-install \
    pdo_mysql \
    mysqli \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd \
    zip \
    dom

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Install WP-CLI globally
RUN curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/wp-cli.phar \
    && chmod +x wp-cli.phar \
    && mv wp-cli.phar /usr/local/bin/wp

# Create working directory
WORKDIR /var/www/html

# Install Node.js and npm (useful for development)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Set up user permissions
RUN groupadd -g 1000 www \
    && useradd -u 1000 -ms /bin/bash -g www www

# Copy the rest of the application
COPY . .

# Note: Dependencies will be installed when container starts
# to avoid SSH key issues during build

# Set proper permissions
RUN chown -R www:www /var/www/html

# Development stage
FROM base AS development

# Switch to www user
USER www

# Expose port for PHP built-in server
EXPOSE 8080

# Default command
CMD ["bash"]
