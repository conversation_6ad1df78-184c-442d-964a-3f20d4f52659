#!/usr/bin/env bash
#
# Execute a command in each subfolder.
#

get_bundle_dependencies()
{
	echo "wp-cli/wp-cli-bundle"
	curl --silent "https://raw.githubusercontent.com/wp-cli/wp-cli-bundle/main/composer.json" \
		| jq --raw-output '."require" | keys[] | select(startswith("wp-cli/"))'
}

while read -r dependency; do
	# Remove wp-cli/ prefix
    repo_path="${dependency:7}"
    if [ ! -d "${repo_path}" ]; then
		continue
	fi

	echo "--- ${repo_path}: ---"
	cd "${repo_path}"
	bash -c "$@"
	cd -
done <<< "$(get_bundle_dependencies)"
