# WP-CLI Docker Development Environment Configuration

# MySQL Configuration
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=wp_cli_dev
MYSQL_USER=wp_cli_dev
MYSQL_PASSWORD=wp_cli_dev_password

# PHP Configuration
PHP_VERSION=8.1

# Port Configuration
PHP_PORT=8080
MYSQL_PORT=3306
PHPMYADMIN_PORT=8081
WORDPRESS_PORT=8082

# Development Settings
WP_CLI_CACHE_DIR=/tmp/wp-cli-cache
COMPOSER_CACHE_DIR=/root/.composer

# WordPress Test Configuration
WORDPRESS_DB_NAME=wordpress_test
WORDPRESS_DB_USER=wp_cli_dev
WORDPRESS_DB_PASSWORD=wp_cli_dev_password
