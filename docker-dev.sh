#!/bin/bash

# WP-CLI Docker Development Helper Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker &> /dev/null; then
        print_error "docker is not installed. Please install it and try again."
        exit 1
    fi

    # Check for docker compose (new) or docker-compose (legacy)
    if ! docker compose version &> /dev/null && ! command -v docker-compose &> /dev/null; then
        print_error "docker compose is not available. Please install Docker Compose and try again."
        exit 1
    fi
}

# Function to get the correct docker compose command
get_compose_cmd() {
    if docker compose version &> /dev/null; then
        echo "docker compose"
    else
        echo "docker-compose"
    fi
}

# Function to check if dependencies need to be installed
check_dependencies() {
    local needs_install=false

    # Check if vendor directory exists and has content
    if [ ! -d "vendor" ] || [ -z "$(ls -A vendor 2>/dev/null)" ]; then
        print_status "Vendor directory is missing or empty, dependencies need to be installed."
        needs_install=true
    fi

    # Check if composer.lock exists and is newer than composer.json
    if [ ! -f "composer.lock" ]; then
        print_status "composer.lock is missing, dependencies need to be installed."
        needs_install=true
    elif [ "composer.json" -nt "composer.lock" ]; then
        print_status "composer.json is newer than composer.lock, dependencies need to be updated."
        needs_install=true
    fi

    echo $needs_install
}

# Function to install dependencies only if needed
install_dependencies() {
    local force_install=${1:-false}
    local needs_install=$(check_dependencies)
    local compose_cmd=$(get_compose_cmd)

    if [ "$force_install" = true ] || [ "$needs_install" = true ]; then
        print_status "Installing/updating Composer dependencies..."
        $compose_cmd exec php composer install

        print_status "Preparing tests..."
        $compose_cmd exec php composer prepare-tests
    else
        print_status "Dependencies are up to date, skipping installation."
    fi
}

# Function to start the development environment
start() {
    print_header "Starting WP-CLI Development Environment"
    check_docker
    check_docker_compose

    local compose_cmd=$(get_compose_cmd)

    print_status "Building and starting containers..."
    $compose_cmd up -d --build

    print_status "Waiting for services to be ready..."
    sleep 10

    # Use smart dependency installation
    install_dependencies

    print_header "Development Environment Ready!"
    echo -e "${GREEN}Services available at:${NC}"
    echo "  - phpMyAdmin: http://localhost:8081"
    echo "  - WordPress Test: http://localhost:8080"
    echo "  - MySQL: localhost:3306"
    echo ""
    echo -e "${GREEN}To access the PHP container:${NC}"
    echo "  $compose_cmd exec php bash"
    echo ""
    echo -e "${GREEN}To run WP-CLI:${NC}"
    echo "  $compose_cmd exec php vendor/bin/wp --info"
}

# Function to stop the development environment
stop() {
    print_header "Stopping WP-CLI Development Environment"
    local compose_cmd=$(get_compose_cmd)
    $compose_cmd down
    print_status "Environment stopped."
}

# Function to restart the development environment
restart() {
    print_header "Restarting WP-CLI Development Environment"
    stop
    start
}

# Function to show logs
logs() {
    local service=${1:-}
    local compose_cmd=$(get_compose_cmd)
    if [ -z "$service" ]; then
        print_status "Showing logs for all services..."
        $compose_cmd logs -f
    else
        print_status "Showing logs for $service..."
        $compose_cmd logs -f "$service"
    fi
}

# Function to run tests
test() {
    print_header "Running WP-CLI Tests"
    local compose_cmd=$(get_compose_cmd)

    print_status "Running linter tests..."
    $compose_cmd exec php composer lint

    print_status "Running PHPCS tests..."
    $compose_cmd exec php composer phpcs

    print_status "Running PHPUnit tests..."
    $compose_cmd exec php composer phpunit

    print_status "Running Behat tests..."
    $compose_cmd exec php composer behat

    print_status "All tests completed!"
}

# Function to enter the PHP container
shell() {
    print_status "Entering PHP container..."
    local compose_cmd=$(get_compose_cmd)
    $compose_cmd exec php bash
}

# Function to clean up everything
clean() {
    print_header "Cleaning Up Development Environment"
    print_warning "This will remove all containers, volumes, and images!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        local compose_cmd=$(get_compose_cmd)
        $compose_cmd down -v --remove-orphans
        docker system prune -a -f
        print_status "Cleanup completed."
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show status
status() {
    print_header "Development Environment Status"
    local compose_cmd=$(get_compose_cmd)
    $compose_cmd ps
}

# Function to force install dependencies
install() {
    print_header "Installing Dependencies"
    install_dependencies true
}

# Function to show help
help() {
    echo "WP-CLI Docker Development Helper"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start     Start the development environment (smart dependency install)"
    echo "  stop      Stop the development environment"
    echo "  restart   Restart the development environment"
    echo "  status    Show container status"
    echo "  logs      Show logs (optionally for specific service)"
    echo "  test      Run all tests"
    echo "  shell     Enter the PHP container"
    echo "  install   Force install/update dependencies"
    echo "  clean     Clean up all containers and volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start          # Start with smart dependency checking"
    echo "  $0 install        # Force install dependencies"
    echo "  $0 logs php       # Show PHP container logs"
    echo "  $0 test           # Run all tests"
    echo "  $0 shell          # Enter PHP container"
    echo ""
    echo "Dependency Management:"
    echo "  Dependencies are only installed/updated when:"
    echo "  - vendor/ directory is missing or empty"
    echo "  - composer.lock is missing"
    echo "  - composer.json is newer than composer.lock"
    echo "  - 'install' command is used to force update"
}

# Main script logic
case "${1:-help}" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$2"
        ;;
    test)
        test
        ;;
    shell)
        shell
        ;;
    install)
        install
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
